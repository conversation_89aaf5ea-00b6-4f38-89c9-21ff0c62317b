import React, { useState } from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";

import { Box } from "@/components/ui/box";
import { Heart } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { router } from "expo-router";
import { TouchableOpacity, Share } from "react-native";
import { ClassStatusButton } from "./class-button";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { obtainDateFrame } from "@/data/common/common.utils";
import {
  obtainSpotsAvailable,
  obtainStatus,
} from "@/data/screens/classes/utils";
import { getButtonStatus } from "./utils";
import { useReserveClass } from "@/data/screens/classes/mutations/useReserveClass";
import { ReservationSuccessActionsheet } from "./reservation-success-actionsheet";
import { addToCalendar, createShareText } from "@/utils/calendar";
import { useCancelReservation } from "@/data/screens/classes/mutations/useCancelReservation";
import { Image } from "@/components/ui/image";

export const ClassCard = (data: ClassDetailsResponse) => {
  const {
    name,
    class_type,
    start_time,
    end_time,
    instructor_first_name,
    instructor_last_name,
    gym_name,
    room_name,
    id,
    images,
    selected_date,
    current_user_reservation,
  } = data;

  const [showSuccessActionsheet, setShowSuccessActionsheet] = useState(false);

  const handleCardPress = () =>
    router.push({
      pathname: "/(class-details)/[id]",
      params: { id, date: selected_date },
    });

  const { mutate: reserveClass, isPending } = useReserveClass(() => {
    setShowSuccessActionsheet(true);
  });

  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelReservation();

  const handleMakeAnotherReservation = () => {
    setShowSuccessActionsheet(false);
    // Navigate to classes list or stay on current page
  };

  const handleAddToCalendar = async () => {
    setShowSuccessActionsheet(false);

    await addToCalendar({
      title: name,
      startTime: start_time,
      endTime: end_time,
      date: selected_date ?? "",
      location: `${gym_name}, ${room_name}`,
      description: `Fitness class with ${instructor_first_name} ${instructor_last_name}`,
      instructor: `${instructor_first_name} ${instructor_last_name}`,
    });
  };

  const handleShareWithFriends = () => {
    setShowSuccessActionsheet(false);

    const shareText = createShareText({
      title: name,
      startTime: start_time,
      endTime: end_time,
      date: selected_date ?? "",
      location: `${gym_name}, ${room_name}`,
      instructor: `${instructor_first_name} ${instructor_last_name}`,
    });

    Share.share({
      message: shareText,
      title: "Class Reservation",
    });
  };

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <VStack
        space="sm"
        className="bg-white rounded-2xl p-4 border border-background-200 mb-2"
      >
        {/* Top row with image, title, status badge, and heart */}
        <HStack space="sm" className="items-start">
          <Image
            source={{ uri: images[0] }}
            className="w-12 h-12 rounded-lg"
            alt={name}
          />
          <VStack className="flex-1" space="xs">
            <HStack className="items-center justify-between">
              <HStack className="items-center flex-1" space="sm">
                <Text
                  className="text-[#00697B] font-dm-sans-bold text-base line-clamp-1 max-w-32"
                  numberOfLines={2}
                >
                  {name}
                </Text>
                {obtainStatus(class_type).text && (
                  <Box
                    className={`px-2 py-1 rounded-md ${
                      obtainStatus(class_type).classes
                    }`}
                  >
                    <Text
                      className={`text-xs font-dm-sans-medium ${
                        obtainStatus(class_type).classes
                      }`}
                    >
                      {obtainStatus(class_type).text}
                    </Text>
                  </Box>
                )}
              </HStack>
              <TouchableOpacity
                className="ml-2"
                onPress={(e) => {
                  e.stopPropagation();
                }}
              >
                <Icon
                  as={Heart}
                  size="sm"
                  className={
                    false // fix later
                      ? "text-error-500 fill-error-500"
                      : "text-typography-400"
                  }
                />
              </TouchableOpacity>
            </HStack>

            {/* Time and spots row */}
            <HStack className="items-center" space="xs">
              <Text className="text-sm font-dm-sans-regular text-typography-600">
                {obtainDateFrame(start_time, end_time)}
              </Text>
              <Box className="w-1 h-1 bg-[#00697B] rounded-full mx-1" />
              <Text className="text-sm font-dm-sans-regular text-typography-600">
                {`${obtainSpotsAvailable(class_type, data)} spots left`}
              </Text>
            </HStack>
          </VStack>
        </HStack>

        {/* Bottom row with instructor, location, and button */}
        <HStack className="justify-between items-center">
          <VStack space="xs">
            <Text className="text-sm font-dm-sans-medium text-typography-900">
              {instructor_first_name} {instructor_last_name}
            </Text>
            <Text className="text-xs font-dm-sans-regular text-typography-600">
              {gym_name}
            </Text>
          </VStack>
          <ClassStatusButton
            onReserve={() =>
              reserveClass({
                class_id: id,
                date: selected_date ?? "",
                is_virtual: Boolean(data.is_virtual),
              })
            }
            onCancelReservation={() => {
              if (current_user_reservation) {
                cancelReservation(current_user_reservation.id as number);
              }
            }}
            onJoinWaitlist={() => {
              // TODO: Implement waitlist functionality
              console.log("Join waitlist for class:", id);
            }}
            status={getButtonStatus(data)}
            isLoading={isPending || isCancelling}
          />
        </HStack>
      </VStack>

      <ReservationSuccessActionsheet
        isOpen={showSuccessActionsheet}
        onClose={() => setShowSuccessActionsheet(false)}
        onMakeAnotherReservation={handleMakeAnotherReservation}
        onAddToCalendar={handleAddToCalendar}
        onShareWithFriends={handleShareWithFriends}
      />
    </TouchableOpacity>
  );
};
